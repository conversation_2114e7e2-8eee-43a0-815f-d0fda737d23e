/*
 * COMP9024 Assignment - Trippy
 * Time Complexity Analysis:
 * Reading input:O(L + W + F) where L = landmarks, W = walking links, F = ferries
 * Finding  route:O(L + W + F) for DFS with ferry constraints and path optimization
 * Overall: O(L + W + F)
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#define MAX_LANDMARKS 100
#define MAX_NAME_LEN 32
#define MAX_FERRIES 1000
#define MAX_PATHS 1000
#define NO_CONNECTION -1

typedef struct
{
    char name[MAX_NAME_LEN];
} Landmark;

typedef struct
{
    int from;
    int to;
    int departTime;
    int arriveTime;
} Ferry;

typedef struct
{
    int from;
    int to;
    int type; // 0 is walk, 1 is ferry
    int start;
    int end;
    int index;
} PathSegment;

typedef struct
{
    PathSegment segs[MAX_LANDMARKS + MAX_FERRIES];
    int sCount;
    int totalTime;
    int start;
    int end;
} CompletePath;

Landmark landmarks[MAX_LANDMARKS];
int matrix[MAX_LANDMARKS][MAX_LANDMARKS];
Ferry ferries[MAX_FERRIES];
int countLands = 0;
int countWalk = 0;
int countFerries = 0;
// Path finding variables
int path[MAX_LANDMARKS];
int pathLen = 0;
int visited[MAX_LANDMARKS];
// Mixed path variables
PathSegment mixedPath[MAX_LANDMARKS + MAX_FERRIES];
int mixPathLen = 0;
// Multiple path variables
CompletePath allPaths[MAX_PATHS];
int pathCount = 0;

// Convert time string (HHMM) to minutes
int timeToMinutes(char *timeStr)
{
    int time = atoi(timeStr);
    int hours = time / 100;
    int minutes = time % 100;
    return hours * 60 + minutes;
}

// Convert minutes since midnight to time string
void minutesToTime(int minutes, char *timeStr)
{
    int hours = minutes / 60;
    int mins = minutes % 60;
    sprintf(timeStr, "%04d", hours * 100 + mins);
}

// Find landmark index by name
int findLand(char *name)
{
    int i;
    for (i = 0; i < countLands; i++)
    {
        if (strcmp(landmarks[i].name, name) == 0)
        {
            return i;
        }
    }
    return -1;
}

// Init matrix
void init()
{
    int i, j;
    for (i = 0; i < MAX_LANDMARKS; i++)
    {
        for (j = 0; j < MAX_LANDMARKS; j++)
        {
            matrix[i][j] = NO_CONNECTION;
        }
    }
}

// Calculate total travel time
int calculateTime(PathSegment *segs, int sCount, int start)
{
    if (sCount == 0)
        return 0;

    int first = segs[0].start;
    int last = segs[sCount - 1].end;

    // If first segment starts later than our start time, include waiting time
    if (first > start)
    {
        return last - start;
    }
    else
    {
        return last - first;
    }
}

// Store cur path as a complete path
void storeCurPath(int start)
{
    if (pathCount >= MAX_PATHS)
        return;

    CompletePath *path = &allPaths[pathCount];
    path->sCount = mixPathLen;
    path->start = start;
    int i;
    for (i = 0; i < mixPathLen; i++)
    {
        path->segs[i] = mixedPath[i];
    }

    if (mixPathLen > 0)
    {
        path->end = mixedPath[mixPathLen - 1].end;
        path->totalTime = calculateTime(path->segs, path->sCount, start);
    }
    else
    {
        path->end = start;
        path->totalTime = 0;
    }

    pathCount++;
}

// Find the path with minimum total time
int findBest()
{
    if (pathCount == 0)
        return -1;

    int index = 0;
    int min = allPaths[0].totalTime;
    int i;
    for (i = 1; i < pathCount; i++)
    {
        if (allPaths[i].totalTime < min)
        {
            min = allPaths[i].totalTime;
            index = i;
        }
    }

    return index;
}

// Print a complete path
void showAll(CompletePath *path)
{
    int i;
    for (i = 0; i < path->sCount; i++)
    {
        PathSegment seg = path->segs[i];
        char start[5], end[5];
        minutesToTime(seg.start, start);
        minutesToTime(seg.end, end);
        if (seg.type == 0)
        { // walking
            int wtime = seg.end - seg.start;
            printf("Walk %d minute(s):\n", wtime);
            printf("  %s %s\n", start, landmarks[seg.from].name);
            printf("  %s %s\n", end, landmarks[seg.to].name);
        }
        else
        { // ferry
            int ftime = seg.end - seg.start;
            printf("Ferry %d minute(s):\n", ftime);
            printf("  %s %s\n", start, landmarks[seg.from].name);
            printf("  %s %s\n", end, landmarks[seg.to].name);
        }
    }
}

// DFS to find path between two landmarks
int dfs(int cur, int target)
{
    if (cur == target)
    {
        path[pathLen++] = cur;
        return 1;
    }
    visited[cur] = 1;
    path[pathLen++] = cur;
    int i;
    for (i = 0; i < countLands; i++)
    {
        if (!visited[i] && matrix[cur][i] != NO_CONNECTION)
        {
            if (dfs(i, target))
            {
                return 1;
            }
        }
    }
    // Backtrack
    pathLen--;
    visited[cur] = 0;
    return 0;
}

// Print walking path with multiple segs
void showWalk(int departTime)
{
    int curTime = departTime;
    int i;
    for (i = 0; i < pathLen - 1; i++)
    {
        int from = path[i];
        int to = path[i + 1];
        int wtime = matrix[from][to];
        char start[5], end[5];
        minutesToTime(curTime, start);
        minutesToTime(curTime + wtime, end);

        printf("Walk %d minute(s):\n", wtime);
        printf("  %s %s\n", start, landmarks[from].name);
        printf("  %s %s\n", end, landmarks[to].name);

        curTime += wtime;
    }
}

// DFS with time constraints
int dfsHasTime(int cur, int target, int curTime)
{
    if (cur == target)
    {
        return 1;
    }

    visited[cur] = 1;
    int i;
    // Try walking to adjacent landmarks
    for (i = 0; i < countLands; i++)
    {
        if (!visited[i] && matrix[cur][i] != NO_CONNECTION)
        {
            int wtime = matrix[cur][i];
            int arrivalTime = curTime + wtime;
            // Add walking segment to path
            mixedPath[mixPathLen].from = cur;
            mixedPath[mixPathLen].to = i;
            mixedPath[mixPathLen].type = 0; // walk
            mixedPath[mixPathLen].start = curTime;
            mixedPath[mixPathLen].end = arrivalTime;
            mixPathLen++;
            if (dfsHasTime(i, target, arrivalTime))
            {
                visited[cur] = 0;
                return 1;
            }
            // Backtrack
            mixPathLen--;
        }
    }
    // Try taking ferries from cur landmark
    for (i = 0; i < countFerries; i++)
    {
        if (ferries[i].from == cur && !visited[ferries[i].to])
        {
            // Check if we can catch this ferry
            if (ferries[i].departTime >= curTime)
            {
                // Add ferry segment to path
                mixedPath[mixPathLen].from = cur;
                mixedPath[mixPathLen].to = ferries[i].to;
                mixedPath[mixPathLen].type = 1; // ferry
                mixedPath[mixPathLen].start = ferries[i].departTime;
                mixedPath[mixPathLen].end = ferries[i].arriveTime;
                mixedPath[mixPathLen].index = i;
                mixPathLen++;
                if (dfsHasTime(ferries[i].to, target, ferries[i].arriveTime))
                {
                    visited[cur] = 0;
                    return 1;
                }
                // Backtrack
                mixPathLen--;
            }
        }
    }
    visited[cur] = 0;
    return 0;
}

// DFS to find ALL paths with time constraint
void dfsAllPaths(int cur, int target, int curTime, int originalstart)
{
    if (cur == target)
    {
        // Store this complete path
        storeCurPath(originalstart);
        return;
    }
    // Mark cur as visited
    visited[cur] = 1;
    int i;
    // Try walking to adjacent landmarks
    for (i = 0; i < countLands; i++)
    {
        if (!visited[i] && matrix[cur][i] != NO_CONNECTION)
        {
            int wtime = matrix[cur][i];
            int arrivalTime = curTime + wtime;
            // Add walking segment to path
            mixedPath[mixPathLen].from = cur;
            mixedPath[mixPathLen].to = i;
            mixedPath[mixPathLen].type = 0; // walk
            mixedPath[mixPathLen].start = curTime;
            mixedPath[mixPathLen].end = arrivalTime;
            mixPathLen++;
            dfsAllPaths(i, target, arrivalTime, originalstart);
            // Backtrack
            mixPathLen--;
        }
    }
    // Try taking ferries from cur landmark
    for (i = 0; i < countFerries; i++)
    {
        if (ferries[i].from == cur && !visited[ferries[i].to])
        {
            // Check if we can catch this ferry
            if (ferries[i].departTime >= curTime)
            {
                // Add ferry segment to path
                mixedPath[mixPathLen].from = cur;
                mixedPath[mixPathLen].to = ferries[i].to;
                mixedPath[mixPathLen].type = 1; // ferry
                mixedPath[mixPathLen].start = ferries[i].departTime;
                mixedPath[mixPathLen].end = ferries[i].arriveTime;
                mixedPath[mixPathLen].index = i;
                mixPathLen++;
                dfsAllPaths(ferries[i].to, target, ferries[i].arriveTime, originalstart);
                // Backtrack
                mixPathLen--;
            }
        }
    }
    // Unmark cur as visited
    visited[cur] = 0;
}

// Print mixed path with walking and ferry segs
void showMix()
{
    int i;
    for (i = 0; i < mixPathLen; i++)
    {
        PathSegment seg = mixedPath[i];
        char start[5], end[5];
        minutesToTime(seg.start, start);
        minutesToTime(seg.end, end);
        if (seg.type == 0)
        { // walking
            int wtime = seg.end - seg.start;
            printf("Walk %d minute(s):\n", wtime);
            printf("  %s %s\n", start, landmarks[seg.from].name);
            printf("  %s %s\n", end, landmarks[seg.to].name);
        }
        else
        { // ferry
            int ftime = seg.end - seg.start;
            printf("Ferry %d minute(s):\n", ftime);
            printf("  %s %s\n", start, landmarks[seg.from].name);
            printf("  %s %s\n", end, landmarks[seg.to].name);
        }
    }
}

// Find and print route
void findRoute(char *fromName, char *toName, char *departtimeStr)
{
    int fromIdx = findLand(fromName);
    int toIdx = findLand(toName);
    int departTime = timeToMinutes(departtimeStr);
    // Check for direct walking connection
    if (matrix[fromIdx][toIdx] != NO_CONNECTION)
    {
        int wtime = matrix[fromIdx][toIdx];
        char start[5], end[5];
        minutesToTime(departTime, start);
        minutesToTime(departTime + wtime, end);
        printf("\nWalk %d minute(s):\n", wtime);
        printf("  %s %s\n", start, fromName);
        printf("  %s %s\n", end, toName);
        return;
    }
    int i;
    // Check for ferry connection
    for (i = 0; i < countFerries; i++)
    {
        if (ferries[i].from == fromIdx && ferries[i].to == toIdx)
        {
            if (ferries[i].departTime >= departTime)
            {
                int ftime = ferries[i].arriveTime - ferries[i].departTime;
                char departStr[5], arriveStr[5];
                minutesToTime(ferries[i].departTime, departStr);
                minutesToTime(ferries[i].arriveTime, arriveStr);
                printf("\nFerry %d minute(s):\n", ftime);
                printf("  %s %s\n", departStr, fromName);
                printf("  %s %s\n", arriveStr, toName);
                return;
            }
        }
    }
    // Try to find optimal path
    // Initialize path finding variables
    pathCount = 0;
    mixPathLen = 0;
    for (i = 0; i < countLands; i++)
    {
        visited[i] = 0;
    }
    // Find all possible paths
    dfsAllPaths(fromIdx, toIdx, departTime, departTime);
    if (pathCount > 0)
    {
        // Find the best path (shortest total time)
        int index = findBest();
        printf("\n");
        showAll(&allPaths[index]);
        return;
    }
    // Initialize path finding variables
    pathLen = 0;
    for (i = 0; i < countLands; i++)
    {
        visited[i] = 0;
    }
    if (dfs(fromIdx, toIdx))
    {
        printf("\n");
        showWalk(departTime);
        return;
    }
    // Fallback to single path search
    mixPathLen = 0;
    for (i = 0; i < countLands; i++)
    {
        visited[i] = 0;
    }
    if (dfsHasTime(fromIdx, toIdx, departTime))
    {
        printf("\n");
        showMix();
        return;
    }
    // No route found
    printf("\nNo available route.\n");
}

int main()
{
    init();
    printf("Number of landmarks: ");
    scanf("%d", &countLands);
    int i;
    for (i = 0; i < countLands; i++)
    {
        scanf("%s", landmarks[i].name);
    }
    printf("Number of walking links: ");
    scanf("%d", &countWalk);
    for (i = 0; i < countWalk; i++)
    {
        char from[MAX_NAME_LEN], to[MAX_NAME_LEN];
        int time;
        scanf("%s", from);
        scanf("%s", to);
        scanf("%d", &time);
        int fromIdx = findLand(from);
        int toIdx = findLand(to);
        // Bidirectional walking link
        matrix[fromIdx][toIdx] = time;
        matrix[toIdx][fromIdx] = time;
    }
    printf("Number of ferry schedules: ");
    scanf("%d", &countFerries);
    for (i = 0; i < countFerries; i++)
    {
        char fromName[MAX_NAME_LEN], toName[MAX_NAME_LEN];
        char departtimeStr[5], arriveTimeStr[5];
        scanf("%s", fromName);
        scanf("%s", departtimeStr);
        scanf("%s", toName);
        scanf("%s", arriveTimeStr);
        ferries[i].from = findLand(fromName);
        ferries[i].to = findLand(toName);
        ferries[i].departTime = timeToMinutes(departtimeStr);
        ferries[i].arriveTime = timeToMinutes(arriveTimeStr);
    }
    // Process queries
    char fromName[MAX_NAME_LEN];
    while (1)
    {
        printf("\nFrom: ");
        scanf("%s", fromName);
        if (strcmp(fromName, "done") == 0)
        {
            printf("Safe travels!\n");
            break;
        }
        char toName[MAX_NAME_LEN], departtime[5];
        printf("To: ");
        scanf("%s", toName);
        printf("Departing at: ");
        scanf("%s", departtime);
        findRoute(fromName, toName, departtime);
    }
    return 0;
}
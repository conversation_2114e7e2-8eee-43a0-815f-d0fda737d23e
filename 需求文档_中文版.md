# COMP9024 25T2 作业 - Trippy

**授课教师：** Dr. Sonit Singh

## 更新日志

我们可能会对规范进行小幅修改，以解决/澄清任何未解决的问题。这些修改可能需要对您的设计/代码进行最小的更改（如果有的话）。强烈建议学生定期查看在线论坛讨论和更新日志。

**版本 1.0**  
(2025-07-04 10:00) 初始发布。

## 目标

本作业旨在为您提供更多独立、自主的练习机会，涉及：

- 高级数据结构，特别是图
- 图算法
- 渐近运行时间分析

## 管理信息

**评分标准：**
- 阶段1：2分（正确性）
- 阶段2：2分（正确性）
- 阶段3：2分（正确性）
- 阶段4：4分（正确性）
- 复杂度分析：1分
- 代码风格：1分
- **总计：12分**

**截止时间：** 第10周周二 16:59:59（8月5日）

**迟交处理：** 每迟交一天扣除最高分的5%，最多扣5天（=120小时）  
例如：如果您迟交25小时，您的分数将减少1.2分（=最高分的10%）

## 项目背景

您在一个新城市，想要规划一次步行游览一些地标的旅行。幸运的是，我们在课堂上学习了许多算法来帮助路径规划。不幸的是，这个城市在步行方面不一定是连通的。为了帮助解决这个问题，有渡轮可以帮助人们出行。

您的目标是编写一个程序，根据用户偏好生成地标之间的最优旅行路线。

## 输入格式

### 地标信息

程序的第一个输入包含一个整数，表示城市中地标的数量，然后是相应行数的地标名称：

```
landmark-name
```

**示例：**
```
./trippy
Number of landmarks: 4
TheRocks
CircularQuay
ManlyWharf
ManlyBeach
```

**假设条件：**
- 输入在语法上是正确的
- 地标名称的最大长度为31个字符，不包含空格
- 名称区分大小写
- 没有名称会被输入超过一次

**提示：**
要读取包含地标名称的单行，您应该使用：
```c
scanf("%s", name);
```
其中 `name` 是一个字符串，即字符数组。

### 步行连接

下一个输入是一个整数，表示地标之间步行连接的数量，然后是相应行数的格式：

```
landmark-1
landmark-2
walking-time
```

其中第三行表示在两个地标之间步行所需的时间（分钟）。注意，这个连接可以双向步行，从 `landmark-1` 到 `landmark-2`，或从 `landmark-2` 到 `landmark-1`。

**示例：**
```
Number of walking links: 2
TheRocks
CircularQuay
6
ManlyWharf
ManlyBeach
8
```

**假设条件：**
- 输入在语法上是正确的
- 只会使用之前输入的地标
- 步行时间将是严格正整数

### 渡轮时刻表

下一个输入是一个整数，表示任何一天的渡轮数量，然后是相应行数的格式：

```
departing-landmark
departing-time
arriving-landmark
arriving-time
```

**示例：**
```
Number of ferry schedules: 2
CircularQuay
0930
ManlyWharf
0952
ManlyWharf
1000
CircularQuay
1022
```

**假设条件：**
- 输入在语法上是正确的
- 只会使用之前输入的地标
- 所有时间都以4位数字格式给出（hhmm - 小时，分钟），有效范围从0000到2359
- 到达时间严格晚于出发时间
- 所有渡轮都在午夜前到达目的地

### 用户查询

程序的最终输入是用户查询：

```
From: TheRocks
To: ManlyBeach
Departing at: 0915
```

**假设条件：**
- 输入在语法上是正确的
- 只会使用之前输入的地标
- 将给出两个不同的地标
- 没有预期的计划会延续到第二天

当用户在 `From:` 提示时输入 "done"，程序应该终止：

```
From: done
Safe travels!
```

## 阶段1（2分）

阶段1要求您从输入生成合适的数据结构。

此阶段的测试用例只会使用查询 `FromLandmark`、`ToLandmark`、`DepartureTime`，满足以下条件：

- `FromLandmark` 和 `ToLandmark` 之间最多存在一个直接连接
- 如果存在这样的连接，这个连接是 `FromLandmark` 和 `ToLandmark` 之间的最短路径
- 如果不存在这样的连接，`FromLandmark` 和 `ToLandmark` 之间将不存在其他路径

**示例：**
```
./trippy
Number of landmarks: 3
CircularQuay
ManlyWharf
TheRocks
Number of walking links: 1
CircularQuay
TheRocks
8
Number of ferry schedules: 1
CircularQuay
1130
ManlyWharf
1152

From: TheRocks
To: CircularQuay
Departing at: 0000

Walk 8 minute(s):
  0000 TheRocks
  0008 CircularQuay

From: CircularQuay
To: ManlyWharf
Departing at: 0100

Ferry 22 minute(s):
  1130 CircularQuay
  1152 ManlyWharf

From: done
Safe travels!
```

如果没有满足要求的连接，输出应该是：`No available route.`

```
From: CircularQuay
To: ManlyWharf
Departing at: 1200

No available route.
```

## 阶段2（2分）

阶段2要求您实现基本的路径查找算法。

此阶段的测试用例只会使用查询 `FromLandmark`、`ToLandmark`、`DepartureTime`，满足以下条件：

- `FromLandmark` 和 `ToLandmark` 之间存在一条且仅有一条简单路径
- 此路径不涉及任何渡轮
- `DepartureTime` 是 `0000`

**示例：**
```
./trippy
Number of landmarks: 7
Barrangaroo
CircularQuay
FingerWharf
RoyalBotanicGardens
SydneyHarbourBridge
SydneyOperaHouse
TheRocks
Number of walking links: 6
Barrangaroo
TheRocks
17
TheRocks
SydneyHarbourBridge
16
TheRocks
CircularQuay
8
CircularQuay
SydneyOperaHouse
6
SydneyOperaHouse
RoyalBotanicGardens
9
RoyalBotanicGardens
FingerWharf
11
Number of ferry schedules: 0

From: Barrangaroo
To: SydneyHarbourBridge
Departing at: 0000

Walk 17 minute(s):
  0000 Barrangaroo
  0017 TheRocks
Walk 16 minute(s):
  0017 TheRocks
  0033 SydneyHarbourBridge

From: SydneyHarbourBridge
To: RoyalBotanicGardens
Departing at: 0000

Walk 16 minute(s):
  0000 SydneyHarbourBridge
  0016 TheRocks
Walk 8 minute(s):
  0016 TheRocks
  0024 CircularQuay
Walk 6 minute(s):
  0024 CircularQuay
  0030 SydneyOperaHouse
Walk 9 minute(s):
  0030 SydneyOperaHouse
  0039 RoyalBotanicGardens

From: done
Safe travels!
```

## 阶段3（2分）

在下一个阶段，您的程序应该找到并输出从 `FromLandmark` 到 `ToLandmark` 的简单路径，满足：

- 可能涉及一个或多个渡轮
- `DepartureTime` 不一定是 `0000`

注意，要登上渡轮，必须在渡轮出发时间之前到达出发地标。

在此阶段的所有测试场景中，最多只有一条满足所有要求的简单路径。

**示例：**
```
./trippy
Number of landmarks: 4
TheRocks
CircularQuay
ManlyWharf
ManlyBeach
Number of walking links: 2
TheRocks
CircularQuay
6
ManlyWharf
ManlyBeach
8
Number of ferry schedules: 2
CircularQuay
1130
ManlyWharf
1152
CircularQuay
1200
ManlyWharf
1222

From: TheRocks
To: ManlyBeach
Departing at: 1125

Walk 6 minute(s):
  1125 TheRocks
  1131 CircularQuay
Ferry 22 minute(s):
  1200 CircularQuay
  1222 ManlyWharf
Walk 8 minute(s):
  1222 ManlyWharf
  1230 ManlyBeach

From: TheRocks
To: ManlyBeach
Departing at: 1200

No available route.

From: done
Safe travels!
```

## 阶段4（4分）

在最后阶段，如果有多条可能的路线，您的程序应该考虑额外的用户偏好：

- 在所有可能的路线中，选择总旅行时间最短的路线

您可以假设永远不会有超过一条具有最短总旅行时间的路线。还要注意，旅行时间包括等待渡轮的任何时间。

**示例：**
```
./trippy
Number of landmarks: 9
Barrangaroo
CircularQuay
DarlingHarbour
FingerWharf
RoyalBotanicGardens
SydneyHarbourBridge
SydneyOperaHouse
TheRocks
WatsonsBay
Number of walking links: 6
Barrangaroo
TheRocks
17
CircularQuay
RoyalBotanicGardens
9
DarlingHarbour
Barrangaroo
8
RoyalBotanicGardens
FingerWharf
11
TheRocks
CircularQuay
8
TheRocks
SydneyHarbourBridge
16
Number of ferry schedules: 1
Barrangaroo
1600
CircularQuay
1611

From: DarlingHarbour
To: FingerWharf
Departing at: 1552

Walk 8 minute(s):
  1552 DarlingHarbour
  1600 Barrangaroo
Ferry 11 minute(s):
  1600 Barrangaroo
  1611 CircularQuay
Walk 9 minute(s):
  1611 CircularQuay
  1620 RoyalBotanicGardens
Walk 11 minute(s):
  1620 RoyalBotanicGardens
  1631 FingerWharf

From: DarlingHarbour
To: FingerWharf
Departing at: 1600

Walk 8 minute(s):
  1600 DarlingHarbour
  1608 Barrangaroo
Walk 17 minute(s):
  1608 Barrangaroo
  1625 TheRocks
Walk 8 minute(s):
  1625 TheRocks
  1633 CircularQuay
Walk 9 minute(s):
  1633 CircularQuay
  1642 RoyalBotanicGardens
Walk 11 minute(s):
  1642 RoyalBotanicGardens
  1653 FingerWharf

From: DarlingHarbour
To: WatsonsBay
Departing at: 1600

No available route.

From: done
Safe travels!
```

## 复杂度分析（1分）

您必须包含对程序渐近最坏情况运行时间的分析，用大O记号表示。这应该基于输入的大小，包括：

- 地标数量
- 步行连接数量
- 渡轮时刻表数量

您的主程序文件 `trippy.c` 应该以注释（`/* … */`）开头，包含程序的时间复杂度（大O记号）以及简要说明。

如果您不确定如何分析具有多个函数或阶段的程序的复杂度，请参考讲座材料。例如，参见最小生成树的Kruskal算法分析，它分解了多阶段算法的复杂度。

## 代码风格（1分）

风格考虑包括：

- 可读性
- 结构化编程
- 良好的注释

没有强制的风格指南，但无论您选择什么风格，都应该保持一致并遵循良好的实践，例如：

- **缩进：** 使用缩进清楚地显示每个代码块的范围
- **空白：** 添加间距以避免过于密集的代码
- **命名：** 为变量和函数选择有意义的名称
- **魔法数字：** 避免硬编码值——使用 `#define` 来命名它们
- **结构：** 将代码分解为合理长度的函数（例如，少于50行），避免不必要的重复
- **注释：** 为复杂或不明显的代码段包含有用、简洁的注释（英文）
- **注释掉的代码：** 从最终提交中删除任何注释掉的代码

## 提示

如果您发现讲座中的以下任何ADT有用，那么您可以，实际上我们鼓励您在程序中使用它们：

- 链表ADT：`list.h`, `list.c`
- 栈ADT：`stack.h`, `stack.c`
- 队列ADT：`queue.h`, `queue.c`
- 优先队列ADT：`PQueue.h`, `PQueue.c`
- 图ADT：`Graph.h`, `Graph.c`
- 加权图ADT：`WGraph.h`, `WGraph.c`

您可以自由修改这六个ADT中的任何一个以用于作业目的（但不要更改文件名）。如果您的程序使用了这些ADT中的一个或多个，您必须提交头文件和实现文件，即使您没有更改它们。

虽然作业以阶段形式呈现，但您的程序不需要识别或标记输入为"阶段1"、"阶段2"等。这些阶段纯粹作为开发指南提供，帮助您逐步构建解决方案并最大化部分分数。

例如，正确的阶段3程序也应该为所有阶段2测试产生正确的输出，正确的阶段4程序应该通过所有阶段3测试。这是因为阶段2的有效输入是阶段3的子集，阶段3和阶段4也是如此。

您可以选择直接实现阶段4。但是，以明确定义的、可测试的阶段构建复杂程序是良好的实践。这样做使调试更容易，并提高您获得部分学分的机会，即使您没有完成整个作业。

两个地标可能同时通过步行连接和渡轮连接。

## 测试

我们创建了一个可以自动测试您程序的脚本。要运行此测试，您可以执行与此作业对应的程序。它期望在当前目录中找到程序 `trippy.c` 和您的程序正在使用的任何可接受的ADT（`list`, `stack`, `queue`, `PQueue`, `Graph`, `WGraph`），即使您使用它们时没有更改。您可以按如下方式使用dryrun：

```
9024 dryrun trippy
```

**请注意：** 通过dryrun并不保证您的程序是正确的。您应该使用自己的测试用例彻底测试您的程序。

## 提交

对于此作业，您需要提交一个名为 `trippy.c` 的文件和您的程序正在使用的任何名为 `list`, `stack`, `queue`, `PQueue`, `Graph`, `WGraph` 的ADT，即使您没有更改它们。您可以通过WebCMS3提交或使用命令行。例如，如果您的程序使用 `Graph` ADT和 `queue` ADT，那么您应该提交：

```
give cs9024 assn trippy.c Graph.h Graph.c queue.h queue.c
```

不要忘记将时间复杂度添加到您的主源代码文件 `trippy.c` 中。

您可以提交任意次数——后续提交将覆盖之前的提交。您可以在WebCMS3上检查或使用以下命令确认您的提交已收到：

```
9024 classrun -check assn
```

## 评分

此项目将首先根据功能进行评分，因此您的程序输出完全正确非常重要，如上面的示例所示。在自动评分中得分很低的提交将由人工查看，如果代码结构良好且有注释，可能会获得一些分数。

生成编译错误的程序将获得很低的分数，无论它们可能具有什么其他优点。一般来说，尝试完成工作的重要部分并正确完成该部分的程序将比尝试完成整个工作但有许多错误的程序获得更多分数。

## 收集

评分完成后，您可以使用以下命令收集您的已评分提交：

```
9024 classrun -collect assn
```

您也可以使用以下命令查看您的分数：

```
9024 classrun -sturec
```

您也可以直接通过WebCMS3从此页面顶部的"收集提交"选项卡收集您的已评分提交。

## 学术诚信

不允许小组提交。您的程序必须完全是您自己的工作。将使用抄袭检测软件对所有提交进行成对比较（包括往年类似评估的提交，如果适用），并将应用严重处罚，包括在UNSW抄袭登记册上登记。

您也不允许提交通过ChatGPT、GitHub Copilot、Gemini或类似自动工具帮助获得的代码。

- 不要从他人那里复制想法或代码
- 不要使用公开可访问的存储库或让任何人看到您的代码
- ChatGPT、GitHub Copilot、Gemini和类似工具生成的代码将被视为抄袭

请参考在线资源以帮助您了解什么是抄袭以及在UNSW如何处理抄袭：

- 学术诚信和抄袭
- UNSW抄袭政策

## 最后...

祝您愉快！

---

**注意：** 复制、发布、张贴、分发或翻译此页面是侵犯版权的行为，将提交给UNSW行为和诚信部门处理。
